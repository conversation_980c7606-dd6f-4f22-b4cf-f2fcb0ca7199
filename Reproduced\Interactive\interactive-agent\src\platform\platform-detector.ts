import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

export interface PlatformInfo {
  platform: NodeJS.Platform;
  isWSL: boolean;
  isMSYS: boolean;
  shell: string;
  canSpawnTerminal: boolean;
  availableTerminals: string[];
  hasDisplay: boolean;
  isHeadless: boolean;
}

export interface TerminalCapabilities {
  canSpawn: boolean;
  preferredTerminal: string | null;
  fallbackTerminals: string[];
}

// Module-level cache
let cachedPlatformInfo: PlatformInfo | null = null;
let cacheTimestamp: number = 0;
const CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes

/**
 * Détecte si nous sommes dans un environnement WSL
 */
function detectWSL(): boolean {
  // Vérification des variables d'environnement WSL
  if (process.env.WSL_DISTRO_NAME || process.env.WSLENV) {
    return true;
  }
  
  // Vérification du fichier /proc/version (Linux uniquement)
  if (os.platform() === 'linux') {
    try {
      const versionInfo = fs.readFileSync('/proc/version', 'utf8');
      return versionInfo.toLowerCase().includes('microsoft') || 
             versionInfo.toLowerCase().includes('wsl');
    } catch {
      // Ignore les erreurs de lecture
    }
  }
  
  return false;
}

/**
 * Détecte si nous sommes dans un environnement MSYS/MinGW
 */
function detectMSYS(): boolean {
  return !!(process.env.MSYSTEM || process.env.MINGW_PREFIX);
}

/**
 * Détecte le shell disponible sur le système
 */
function detectShell(): string {
  // Windows
  if (os.platform() === 'win32') {
    return process.env.ComSpec || 'cmd.exe';
  }
  
  // Unix-like systems
  if (process.env.SHELL) {
    return process.env.SHELL;
  }
  
  // Fallback: chercher les shells communs
  const commonShells = ['/bin/bash', '/bin/sh', '/bin/zsh', '/bin/fish'];
  for (const shell of commonShells) {
    try {
      if (fs.existsSync(shell)) {
        return shell;
      }
    } catch {
      // Ignore les erreurs d'accès
    }
  }
  
  return '/bin/sh'; // Fallback ultime
}

/**
 * Vérifie si l'environnement a un affichage graphique
 */
function hasGraphicalDisplay(): boolean {
  // Unix-like: vérifier DISPLAY
  if (process.env.DISPLAY) {
    return true;
  }
  
  // macOS: toujours considéré comme ayant un affichage
  if (os.platform() === 'darwin') {
    return true;
  }
  
  // Windows: toujours considéré comme ayant un affichage
  if (os.platform() === 'win32') {
    return true;
  }
  
  // Wayland sur Linux
  if (process.env.WAYLAND_DISPLAY) {
    return true;
  }
  
  return false;
}

/**
 * Détecte si l'environnement est headless (sans interface graphique)
 */
function isHeadlessEnvironment(): boolean {
  // Variables d'environnement CI/CD communes
  const ciEnvVars = ['CI', 'CONTINUOUS_INTEGRATION', 'GITHUB_ACTIONS', 'GITLAB_CI', 'JENKINS_URL'];
  if (ciEnvVars.some(envVar => process.env[envVar])) {
    return true;
  }
  
  // SSH sans X11 forwarding
  if (process.env.SSH_CLIENT && !process.env.DISPLAY) {
    return true;
  }
  
  // Docker container
  if (fs.existsSync('/.dockerenv')) {
    return true;
  }
  
  return false;
}

/**
 * Détecte les terminaux disponibles sur le système
 */
function detectAvailableTerminals(): string[] {
  const platform = os.platform();
  const terminals: string[] = [];
  
  if (platform === 'win32') {
    // Windows terminals
    const windowsTerminals = [
      'wt.exe', // Windows Terminal
      'ConEmu64.exe',
      'ConEmu.exe',
      'cmd.exe'
    ];
    
    for (const terminal of windowsTerminals) {
      // Vérification simplifiée - dans un vrai cas, on utiliserait where.exe
      terminals.push(terminal);
    }
  } else if (platform === 'darwin') {
    // macOS terminals
    terminals.push('Terminal.app');
    if (fs.existsSync('/Applications/iTerm.app')) {
      terminals.push('iTerm.app');
    }
  } else {
    // Linux terminals
    const linuxTerminals = [
      'gnome-terminal',
      'konsole',
      'xfce4-terminal',
      'mate-terminal',
      'xterm',
      'urxvt'
    ];
    
    for (const terminal of linuxTerminals) {
      // Vérification simplifiée - dans un vrai cas, on utiliserait which
      terminals.push(terminal);
    }
  }
  
  return terminals;
}

/**
 * Détermine si le spawn de terminal est possible
 */
function canSpawnTerminal(platformInfo: Partial<PlatformInfo>): boolean {
  // Pas de spawn en environnement headless
  if (platformInfo.isHeadless) {
    return false;
  }
  
  // Pas de spawn sans affichage graphique (sauf Windows)
  if (!platformInfo.hasDisplay && os.platform() !== 'win32') {
    return false;
  }
  
  // Doit avoir au moins un terminal disponible
  return (platformInfo.availableTerminals?.length || 0) > 0;
}

/**
 * Obtient les informations de plateforme (avec cache)
 */
export function getPlatformInfo(forceRefresh: boolean = false): PlatformInfo {
  const now = Date.now();
  
  // Utiliser le cache si disponible et valide
  if (!forceRefresh && cachedPlatformInfo && (now - cacheTimestamp) < CACHE_TTL_MS) {
    return cachedPlatformInfo;
  }
  
  // Détecter les informations de plateforme
  const platform = os.platform();
  const isWSL = detectWSL();
  const isMSYS = detectMSYS();
  const shell = detectShell();
  const hasDisplay = hasGraphicalDisplay();
  const isHeadless = isHeadlessEnvironment();
  const availableTerminals = detectAvailableTerminals();
  
  const platformInfo: PlatformInfo = {
    platform,
    isWSL,
    isMSYS,
    shell,
    hasDisplay,
    isHeadless,
    availableTerminals,
    canSpawnTerminal: false // Sera défini ci-dessous
  };
  
  platformInfo.canSpawnTerminal = canSpawnTerminal(platformInfo);
  
  // Mettre en cache
  cachedPlatformInfo = platformInfo;
  cacheTimestamp = now;
  
  return platformInfo;
}

/**
 * Rafraîchit le cache de détection de plateforme
 */
export function refreshPlatformCache(): PlatformInfo {
  return getPlatformInfo(true);
}

/**
 * Obtient les capacités de terminal pour la plateforme actuelle
 */
export function getTerminalCapabilities(): TerminalCapabilities {
  const platformInfo = getPlatformInfo();
  
  return {
    canSpawn: platformInfo.canSpawnTerminal,
    preferredTerminal: platformInfo.availableTerminals[0] || null,
    fallbackTerminals: platformInfo.availableTerminals.slice(1)
  };
}
