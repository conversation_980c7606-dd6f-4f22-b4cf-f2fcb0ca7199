export interface PlatformInfo {
    platform: NodeJS.Platform;
    isWSL: boolean;
    isMSYS: boolean;
    shell: string;
    canSpawnTerminal: boolean;
    availableTerminals: string[];
    hasDisplay: boolean;
    isHeadless: boolean;
}
export interface TerminalCapabilities {
    canSpawn: boolean;
    preferredTerminal: string | null;
    fallbackTerminals: string[];
}
/**
 * Obtient les informations de plateforme (avec cache)
 */
export declare function getPlatformInfo(forceRefresh?: boolean): PlatformInfo;
/**
 * Rafraîchit le cache de détection de plateforme
 */
export declare function refreshPlatformCache(): PlatformInfo;
/**
 * Obtient les capacités de terminal pour la plateforme actuelle
 */
export declare function getTerminalCapabilities(): TerminalCapabilities;
//# sourceMappingURL=platform-detector.d.ts.map